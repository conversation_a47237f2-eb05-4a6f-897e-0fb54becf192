import express from "express";
import {
  createPayment,
  getPayments,
  getPaymentById,
  updatePayment,
  deletePayment,
  getPaymentsWithPagination,
} from "../controllers/payment.controller.js";

import { authorize } from "../middlewares/auth.middleware.js";
import { validatePayment } from "../validators/payment.validator.js";

const router = express.Router();

// Apply authorization middleware to all routes
router.use(authorize(["admin", "super_admin"]));

// Create a new payment
router.post("/", validatePayment, createPayment);

// Get all payments
router.get("/", getPayments);

// Get payments with pagination
router.get("/pagination", getPaymentsWithPagination);

// Get a payment by ID
router.get("/:id", getPaymentById);

// Update a payment
router.put("/:id", validatePayment, updatePayment);

// Delete a payment
router.delete("/:id", deletePayment);

export default router;
