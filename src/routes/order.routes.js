import express from "express";
import {
  createOrder,
  getOrders,
  getOrderById,
  updateOrder,
  deleteOrder,
  getOrdersWithPagination,
  getOrdersWithCursorPagination,
  getOrdersWithAggregationPagination,
} from "../controllers/order.controller.js";
import { validateOrder } from "../validators/order.validator.js";
import { authorize } from "../middlewares/auth.middleware.js";

const router = express.Router();


// Apply authorization middleware to all routes
router.use(authorize(["admin", "super_admin"]));

// Create a new order
router.post("/", validateOrder, createOrder);

// Get all orders
router.get("/", getOrders);

router.get("/pagination", getOrdersWithPagination);

// Get all orders with pagination
router.get("/cursor", getOrdersWithCursorPagination);
router.get("/orders/aggregate", getOrdersWithAggregationPagination);

// Get an order by ID
router.get("/:id", getOrderById);

// Update an order by ID
router.put("/:id", validateOrder, updateOrder);

// Delete an order by ID
router.delete("/:id", deleteOrder);

export default router;
