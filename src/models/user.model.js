import mongoose from "mongoose";
import { ROLES } from "../utils/constants.js";

const userSchema = new mongoose.Schema(
  {
    // Authentication Details
    clerkId: { type: String, unique: true },
    email: { type: String, required: true, unique: true },
    username: { type: String, unique: true, required: true },
    password: { type: String },
    googleId: { type: String, unique: true },
    provider: {
      type: String,
      enum: ["clerk", "google", "email"],
      default: "clerk",
    },

    // Profile Details
    firstName: { type: String },
    lastName: { type: String },
    profileImage: { type: String },
    phoneNumber: { type: String },
    address: {
      street: { type: String },
      city: { type: String },
      state: { type: String },
      postalCode: { type: String },
      country: { type: String },
    },

    // Role and Access
    role: { type: String, enum: Object.values(ROLES), default: ROLES.USER },
    isDeleted: { type: Boolean, default: false },

    // Status and Metadata
    lastLogin: { type: Date },
    createdBy: { type: String, enum: ["self", "admin"], default: "self" },
  },
  { timestamps: true }
);

// Virtual field to fetch the current active subscription
userSchema.virtual("activeSubscription", {
  ref: "Subscription",
  localField: "_id",
  foreignField: "userId",
  justOne: true, // Ensure it only returns the latest subscription
  options: { sort: { endDate: -1 }, match: { isActive: true } },
});

export default mongoose.model("User", userSchema);
