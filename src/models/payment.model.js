import mongoose from "mongoose";
import { PAYMENT_STATUSES } from "../utils/constants.js";

const paymentSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    amount: { type: Number, required: true },
    currency: { type: String, required: true, default: "USD" },
    status: {
      type: String,
      enum: Object.values(PAYMENT_STATUSES),
      default: PAYMENT_STATUSES.PENDING,
    },
    method: {
      type: String,
      enum: ["credit_card", "paypal", "bank_transfer"],
      required: true,
    },
    transactionId: { type: String },
    description: { type: String },
    isRefunded: { type: Boolean, default: false },
    paymentMethodDetails: {
      type: Object, // Store sensitive payment details securely here, if needed
      required: false,
    },
  },
  { timestamps: true }
);

export default mongoose.model("Payment", paymentSchema);
