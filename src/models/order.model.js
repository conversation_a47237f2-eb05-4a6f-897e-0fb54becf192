import mongoose from "mongoose";

const orderSchema = new mongoose.Schema(
  {
    user: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    products: [{ type: mongoose.Schema.Types.ObjectId, ref: "Product" }],
    // products: [
    //   {
    //     product: { type: mongoose.Schema.Types.ObjectId, ref: "Product" },
    //   },
    // ],
    totalAmount: { type: Number, required: true },
    payment: { type: mongoose.Schema.Types.ObjectId, ref: "Payment" },
    status: {
      type: String,
      enum: ["completed", "failed", "pending"],
      default: "pending",
    },
    deliveryEmail: { type: String },
    shippingAddress: {
      street: { type: String },
      city: { type: String },
      state: { type: String },
      postalCode: { type: String },
      country: { type: String },
    }, // Add shipping address for physical goods
  },
  { timestamps: true }
);

export default mongoose.model("Order", orderSchema);
