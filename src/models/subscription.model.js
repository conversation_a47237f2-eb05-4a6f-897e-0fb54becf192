import mongoose from "mongoose";

const subscriptionSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    planId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan",
      required: true,
    },
    startDate: { type: Date, required: true, default: Date.now },
    endDate: { type: Date },
    isActive: { type: Boolean, default: true },
    isCurrent: { type: Boolean, default: false }, // Indicates the active subscription
    autoRenew: { type: Boolean, default: true },
    paymentMethod: { type: String },
    paymentStatus: {
      type: String,
      enum: ["paid", "failed", "pending"],
      default: "pending",
    },
    history: [
      {
        planId: { type: mongoose.Schema.Types.ObjectId, ref: "Plan" },
        startDate: { type: Date },
        endDate: { type: Date },
        paymentStatus: { type: String },
        isActive: { type: Boolean },
      },
    ], // Tracks past subscription history
  },
  { timestamps: true }
);

// Middleware to ensure only one active "current" subscription exists per user
subscriptionSchema.pre("save", async function (next) {
  if (this.isCurrent) {
    await mongoose
      .model("Subscription")
      .updateMany(
        { userId: this.userId, isCurrent: true },
        { isCurrent: false }
      );
  }
  next();
});

export default mongoose.model("Subscription", subscriptionSchema);
