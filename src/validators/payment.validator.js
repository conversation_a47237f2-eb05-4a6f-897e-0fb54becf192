import { check, validationResult } from "express-validator";
import { PAYMENT_STATUSES } from "../utils/constants.js";

export const validatePayment = [
  check("userId").notEmpty().withMessage("User ID is required"),
  check("amount")
    .isNumeric()
    .withMessage("Amount must be a numeric value")
    .notEmpty()
    .withMessage("Amount is required"),
  check("currency")
    .isString()
    .withMessage("Currency must be a string")
    .notEmpty()
    .withMessage("Currency is required"),
  check("method")
    .isIn(["credit_card", "paypal", "bank_transfer"])
    .withMessage("Invalid payment method"),
  check("status")
    .optional()
    .isIn(Object.values(PAYMENT_STATUSES))
    .withMessage("Invalid payment status"),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];
