import { check, validationResult } from "express-validator";

export const validateOrder = [
  check("user")
    .notEmpty()
    .withMessage("User ID is required")
    .isMongoId()
    .withMessage("Invalid User ID format"),
  check("templates")
    .isArray()
    .withMessage("Templates must be an array")
    .custom((value) => value.length > 0)
    .withMessage("Templates array cannot be empty")
    .bail(),
  check("totalAmount")
    .isNumeric()
    .withMessage("Total amount must be a number")
    .notEmpty()
    .withMessage("Total amount is required"),
  check("payment")
    .optional()
    .isMongoId()
    .withMessage("Invalid Payment ID format"),
  check("status")
    .optional()
    .isIn(["completed", "failed", "pending"])
    .withMessage("Invalid order status"),
  check("deliveryEmail")
    .optional()
    .isEmail()
    .withMessage("Invalid email format"),
  check("shippingAddress")
    .optional()
    .isObject()
    .withMessage("Shipping address must be an object")
    .custom((value) => {
      if (
        value &&
        (!value.street || !value.city || !value.state || !value.postalCode || !value.country)
      ) {
        throw new Error("Shipping address must contain all fields");
      }
      return true;
    }),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];
