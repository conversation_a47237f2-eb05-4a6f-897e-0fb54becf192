import Payment from "../models/payment.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";

/**
 * Create a new payment record
 */
export const createPayment = asyncHandler(async (req, res) => {
  const { user, amount, currency, method, transactionId, description } =
    req.body;

  const newPayment = new Payment({
    user,
    amount,
    currency,
    method,
    transactionId,
    description,
  });

  const savedPayment = await newPayment.save();
  res.status(201).json({ success: true, payment: savedPayment });
});

/**
 * Get all payment records
 */
export const getPayments = asyncHandler(async (req, res) => {
  const payments = await Payment.find()
  // .populate("user", "name email");
  res.status(200).json({ success: true, payments });
});

/**
 * Get all payment with pagination
 */

export const getPaymentsWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  const total = await Payment.countDocuments();
  const pages = Math.ceil(total / limit);
  const payments = await Payment.find()
    // .populate("user", "name email")
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    success: true,
    data: payments,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

/**
 * Get a payment by ID
 */
export const getPaymentById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const payment = await Payment.findById(id);
  // .populate("user", "name email");

  if (!payment) {
    res.status(404);
    throw new Error("Payment not found");
  }

  res.status(200).json({ success: true, payment });
});

/**
 * Update a payment record
 */
export const updatePayment = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const updatedPayment = await Payment.findByIdAndUpdate(id, updates, {
    new: true,
  });

  if (!updatedPayment) {
    res.status(404);
    throw new Error("Payment not found");
  }

  res.status(200).json({ success: true, payment: updatedPayment });
});

/**
 * Delete a payment record
 */
export const deletePayment = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedPayment = await Payment.findByIdAndDelete(id);

  if (!deletedPayment) {
    res.status(404);
    throw new Error("Payment not found");
  }

  res
    .status(200)
    .json({ success: true, message: "Payment record deleted successfully" });
});
