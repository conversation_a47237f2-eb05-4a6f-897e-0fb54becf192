import Order from "../models/order.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";

/**
 * Create a new order
 */
export const createOrder = asyncHandler(async (req, res) => {
  const {
    user,
    products,
    totalAmount,
    payment,
    status,
    deliveryEmail,
    shippingAddress,
  } = req.body;

  const newOrder = new Order({
    user,
    products,
    totalAmount,
    payment,
    status,
    deliveryEmail,
    shippingAddress,
  });

  const savedOrder = await newOrder.save();
  res.status(201).json({ success: true, order: savedOrder });
});

/**
 * Get all orders
 */
export const getOrders = asyncHandler(async (req, res) => {
  const orders = await Order.find()
    // .populate("user", "name email")
    // .populate("payment", "status")
    // .populate("products", "name price");
  // .populate("products.product", "name price");
  res.status(200).json({ success: true, data: orders });
});

/**
 * Get all orders with pagination
 */
// GET /orders?page=2&limit=10
export const getOrdersWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const skip = (page - 1) * limit;

  const totalOrders = await Order.countDocuments();

  const orders = await Order.find()
    // .populate("user", "name email")
    // .populate("products", "name price")
    // .populate("payment", "status amount method")
    .skip(skip)
    .limit(limit)
    .sort({ createdAt: -1 });

  const totalPages = Math.ceil(totalOrders / limit);

  res.status(200).json({
    success: true,
    data: orders,
    currentPage: page,
    totalOrders,
    totalPages,
    hasNextPage: page < totalPages,
  });
});

// Get all orders with cursor based pagination
// GET /orders/cursor?limit=10&cursor=<lastOrderId>
export const getOrdersWithCursorPagination = asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit, 10) || 10; // Default limit
  const cursor = req.query.cursor || null; // Get the cursor from query

  const query = cursor ? { _id: { $lt: cursor } } : {}; // Filter if cursor exists

  const orders = await Order.find(query)
    // .populate("user", "name email") // Populate user fields
    // .populate("products", "name price") // Populate product details
    // .populate("payment", "status amount method") // Populate payment details
    .sort({ _id: -1 }) // Sort by descending _id (newest first)
    .limit(limit + 1); // Fetch one extra document to determine next cursor

  const hasNextPage = orders.length > limit; // Check if there's another page
  const data = hasNextPage ? orders.slice(0, -1) : orders; // Remove the extra document if hasNextPage

  const nextCursor = hasNextPage ? data[data.length - 1]._id : null; // Get next cursor if available

  res.status(200).json({
    success: true,
    data,
    nextCursor,
    hasNextPage,
  });
});

// GET /orders/aggregate?page=2&limit=10

export const getOrdersWithAggregationPagination = asyncHandler(
  async (req, res) => {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const results = await Order.aggregate([
      {
        $facet: {
          data: [
            { $sort: { createdAt: -1 } }, // Sort by newest first
            { $skip: skip },
            { $limit: limit },
            {
              $lookup: {
                from: "users", // Name of the User collection
                localField: "user",
                foreignField: "_id",
                as: "user",
              },
            },
            {
              $lookup: {
                from: "products", // Name of the Product collection
                localField: "products",
                foreignField: "_id",
                as: "products",
              },
            },
          ],
          totalDocuments: [{ $count: "count" }],
        },
      },
    ]);

    const totalOrders = results[0].totalDocuments[0]?.count || 0;
    const totalPages = Math.ceil(totalOrders / limit);
    const orders = results[0].data;

    res.status(200).json({
      success: true,
      data: orders,
      currentPage: page,
      totalOrders,
      totalPages,
      hasNextPage: page < totalPages,
    });
  }
);

/**
 * Get an order by ID
 */
export const getOrderById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const order = await Order.findById(id)
    // .populate("user", "name email")
    // .populate("payment", "status")
    // .populate("products", "name price");

  if (!order) {
    res.status(404);
    throw new Error("Order not found");
  }

  res.status(200).json({ success: true, order });
});

/**
 * Update an order by ID
 */
export const updateOrder = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const updatedOrder = await Order.findByIdAndUpdate(id, updates, {
    new: true,
  });

  if (!updatedOrder) {
    res.status(404);
    throw new Error("Order not found");
  }

  res.status(200).json({ success: true, order: updatedOrder });
});

/**
 * Delete an order by ID
 */
export const deleteOrder = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedOrder = await Order.findByIdAndDelete(id);

  if (!deletedOrder) {
    res.status(404);
    throw new Error("Order not found");
  }

  res
    .status(200)
    .json({ success: true, message: "Order deleted successfully" });
});
